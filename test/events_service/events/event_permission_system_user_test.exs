defmodule EventsService.Events.EventPermissionSystemUserTest do
  use EventsService.DataCase

  alias EventsService.Events.EventPermission
  alias EventsService.Events
  alias EventsService.Repo

  import EventsService.Factory

  describe "is_system_user field" do
    test "automatically sets is_system_user to true for security users with sec_ prefix" do
      event = insert(:event)
      
      attrs = %{
        event_id: event.id,
        user_document_id: "sec_abc123def456",
        role: [:SECURITY]
      }

      changeset = EventPermission.changeset(%EventPermission{}, attrs)
      
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :is_system_user) == true
    end

    test "does not set is_system_user for regular users" do
      event = insert(:event)
      
      attrs = %{
        event_id: event.id,
        user_document_id: "regular_user_123",
        role: [:EVENT_ADMIN]
      }

      changeset = EventPermission.changeset(%EventPermission{}, attrs)
      
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :is_system_user) != true
    end

    test "does not set is_system_user for users with sec_ prefix but without SECURITY role" do
      event = insert(:event)
      
      attrs = %{
        event_id: event.id,
        user_document_id: "sec_abc123def456",
        role: [:EVENT_ADMIN]
      }

      changeset = EventPermission.changeset(%EventPermission{}, attrs)
      
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :is_system_user) != true
    end

    test "allows manual override of is_system_user field" do
      event = insert(:event)
      
      attrs = %{
        event_id: event.id,
        user_document_id: "regular_user_123",
        role: [:SECURITY],
        is_system_user: true
      }

      changeset = EventPermission.changeset(%EventPermission{}, attrs)
      
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :is_system_user) == true
    end
  end

  describe "get_security_user_by_event_id/1" do
    test "finds security user using is_system_user flag" do
      event = insert(:event)
      
      # Create a regular security user (should not be found)
      insert(:event_permission, 
        event: event, 
        user_document_id: "regular_security_user",
        role: [:SECURITY],
        is_system_user: false
      )
      
      # Create a system security user (should be found)
      system_permission = insert(:event_permission, 
        event: event, 
        user_document_id: "sec_system_user",
        role: [:SECURITY],
        is_system_user: true
      )

      result = Events.get_security_user_by_event_id(event.id)
      
      assert result.id == system_permission.id
      assert result.is_system_user == true
    end

    test "returns nil when no system security user exists" do
      event = insert(:event)
      
      # Create only regular security users
      insert(:event_permission, 
        event: event, 
        user_document_id: "regular_security_user",
        role: [:SECURITY],
        is_system_user: false
      )

      result = Events.get_security_user_by_event_id(event.id)
      
      assert result == nil
    end
  end

  describe "migration compatibility" do
    test "existing records without is_system_user field work correctly" do
      event = insert(:event)
      
      # Simulate existing record without is_system_user set
      {:ok, permission} = 
        %EventPermission{}
        |> EventPermission.migration_changeset(%{
          event_id: event.id,
          user_document_id: "sec_legacy_user",
          role: [:SECURITY]
        })
        |> Repo.insert()

      # The record should exist but is_system_user should be false (default)
      assert permission.is_system_user == false
      
      # After backfill, it should be updated
      from(ep in EventPermission, where: ep.id == ^permission.id)
      |> Repo.update_all(set: [is_system_user: true])
      
      updated_permission = Repo.get(EventPermission, permission.id)
      assert updated_permission.is_system_user == true
    end
  end
end
