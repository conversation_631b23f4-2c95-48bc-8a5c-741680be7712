defmodule EventsService.Events.VariantQuotaTest do
  use ExUnit.Case, async: true

  alias EventsService.Events.VariantQuota

  describe "get_variant_quota/1" do
    test "returns quota minus reserved quota for REGULAR distribution type" do
      variant = %{
        distribution_type: :REGULAR,
        ticket_category: %{quota: 100, reserved_quota: 20}
      }

      assert VariantQuota.get_variant_quota(variant) == 80
    end

    test "returns amount of objects for SALES_CHANNEL distribution type" do
      variant = %{
        distribution_type: :SALES_CHANNEL,
        sales_channel: %{channel_config: %{amount_of_objects: 50}}
      }

      assert VariantQuota.get_variant_quota(variant) == 50
    end

    test "returns nil for GUEST_LIST_INVITATION distribution type" do
      variant = %{distribution_type: :GUEST_LIST_INVITATION}

      assert VariantQuota.get_variant_quota(variant) == nil
    end

    test "returns nil for unknown distribution type" do
      variant = %{distribution_type: :UNKNOWN}

      assert VariantQuota.get_variant_quota(variant) == nil
    end

    test "returns nil for variant without distribution_type" do
      variant = %{}

      assert VariantQuota.get_variant_quota(variant) == nil
    end

    test "returns nil for REGULAR type with missing ticket_category data" do
      variant = %{distribution_type: :REGULAR}

      assert VariantQuota.get_variant_quota(variant) == nil
    end

    test "returns nil for SALES_CHANNEL type with missing sales_channel data" do
      variant = %{distribution_type: :SALES_CHANNEL}

      assert VariantQuota.get_variant_quota(variant) == nil
    end
  end

  describe "get_remaining_quota/1" do
    test "returns nil when quota is nil" do
      variant = %{quota: nil}

      assert VariantQuota.get_remaining_quota(variant) == nil
    end

    test "returns quota when variant_counter is nil" do
      variant = %{quota: 100, variant_counter: nil}

      assert VariantQuota.get_remaining_quota(variant) == 100
    end

    test "returns quota minus sold when variant_counter has sold items" do
      variant = %{quota: 100, variant_counter: %{sold: 30}}

      assert VariantQuota.get_remaining_quota(variant) == 70
    end

    test "returns nil for variant without quota field" do
      variant = %{variant_counter: %{sold: 10}}

      assert VariantQuota.get_remaining_quota(variant) == nil
    end

    test "handles zero sold items" do
      variant = %{quota: 50, variant_counter: %{sold: 0}}

      assert VariantQuota.get_remaining_quota(variant) == 50
    end

    test "handles sold items equal to quota" do
      variant = %{quota: 25, variant_counter: %{sold: 25}}

      assert VariantQuota.get_remaining_quota(variant) == 0
    end
  end

  describe "get_public_remaining_quota/1" do
    test "returns nil for ACTIVE GUEST_LIST_INVITATION variants" do
      variant = %{status: :ACTIVE, distribution_type: :GUEST_LIST_INVITATION}

      assert VariantQuota.get_public_remaining_quota(variant) == nil
    end

    test "returns variant quota for ACTIVE REGULAR variants with nil ticket_category_counter" do
      variant = %{
        status: :ACTIVE,
        distribution_type: :REGULAR,
        ticket_category: %{
          quota: 100,
          reserved_quota: 20,
          ticket_category_counter: nil
        }
      }

      assert VariantQuota.get_public_remaining_quota(variant) == 80
    end

    test "calculates quota with existing items for ACTIVE REGULAR variants" do
      variant = %{
        status: :ACTIVE,
        distribution_type: :REGULAR,
        ticket_category: %{
          quota: 100,
          reserved_quota: 20,
          ticket_category_counter: %{
            existing_items: 30,
            existing_reserved_items: 5
          }
        }
      }

      # quota - reserved_quota - existing_items + existing_reserved_items
      # 100 - 20 - 30 + 5 = 55
      assert VariantQuota.get_public_remaining_quota(variant) == 55
    end

    test "calculates quota minus existing items for ACTIVE SALES_CHANNEL variants" do
      variant = %{
        status: :ACTIVE,
        distribution_type: :SALES_CHANNEL,
        sales_channel: %{channel_config: %{amount_of_objects: 50}},
        variant_counter: %{existing_items: 15}
      }

      assert VariantQuota.get_public_remaining_quota(variant) == 35
    end

    test "returns max(0, quota - sold) for ACTIVE variants with quota and sold" do
      variant = %{
        status: :ACTIVE,
        quota: 100,
        variant_counter: %{sold: 30}
      }

      assert VariantQuota.get_public_remaining_quota(variant) == 70
    end

    test "returns 0 when sold exceeds quota for ACTIVE variants" do
      variant = %{
        status: :ACTIVE,
        quota: 50,
        variant_counter: %{sold: 60}
      }

      assert VariantQuota.get_public_remaining_quota(variant) == 0
    end

    test "returns nil for non-ACTIVE variants" do
      variant = %{
        status: :INACTIVE,
        distribution_type: :REGULAR,
        quota: 100
      }

      assert VariantQuota.get_public_remaining_quota(variant) == nil
    end

    test "returns nil for variants that don't match any pattern" do
      variant = %{status: :ACTIVE}

      assert VariantQuota.get_public_remaining_quota(variant) == nil
    end
  end
end
