defmodule Mix.Tasks.BackfillSystemUsers do
  @moduledoc """
  Backfills the is_system_user field for existing EventPermission records.
  
  This task identifies security users by the 'sec_' prefix in their user_document_id
  and sets is_system_user to true for those records.
  
  Usage:
    mix backfill_system_users
    mix backfill_system_users --dry-run
  """
  
  use Mix.Task
  
  import Ecto.Query
  alias EventsService.Repo
  alias EventsService.Events.EventPermission
  
  require Logger

  @shortdoc "Backfills is_system_user field for existing security users"

  def run(args) do
    Mix.Task.run("app.start")
    
    dry_run = "--dry-run" in args
    
    if dry_run do
      Logger.info("Running in DRY RUN mode - no changes will be made")
    end
    
    Logger.info("Starting backfill of is_system_user field...")
    
    # Find all EventPermission records with user_document_id starting with 'sec_'
    query = 
      from(ep in EventPermission,
        where: like(ep.user_document_id, ^"sec_%"),
        where: ep.is_system_user == false or is_nil(ep.is_system_user)
      )
    
    security_permissions = Repo.all(query)
    
    Logger.info("Found #{length(security_permissions)} security user permissions to update")
    
    if length(security_permissions) == 0 do
      Logger.info("No records to update. Backfill complete.")
      return
    end
    
    # Log some examples for verification
    security_permissions
    |> Enum.take(5)
    |> Enum.each(fn permission ->
      Logger.info("Example: Event #{permission.event_id}, User: #{permission.user_document_id}, Role: #{inspect(permission.role)}")
    end)
    
    if dry_run do
      Logger.info("DRY RUN: Would update #{length(security_permissions)} records")
      return
    end
    
    # Perform the update in batches
    batch_size = 100
    total_updated = 
      security_permissions
      |> Enum.chunk_every(batch_size)
      |> Enum.with_index()
      |> Enum.reduce(0, fn {batch, index}, acc ->
        Logger.info("Processing batch #{index + 1} (#{length(batch)} records)...")
        
        batch_ids = Enum.map(batch, & &1.id)
        
        {updated_count, _} = 
          from(ep in EventPermission, where: ep.id in ^batch_ids)
          |> Repo.update_all(set: [is_system_user: true, updated_at: DateTime.utc_now()])
        
        Logger.info("Updated #{updated_count} records in batch #{index + 1}")
        acc + updated_count
      end)
    
    Logger.info("Backfill complete! Updated #{total_updated} EventPermission records")
    
    # Verification query
    verification_query = 
      from(ep in EventPermission,
        where: like(ep.user_document_id, ^"sec_%"),
        where: ep.is_system_user == true,
        select: count(ep.id)
      )
    
    verified_count = Repo.one(verification_query)
    Logger.info("Verification: #{verified_count} records now have is_system_user = true")
  end
end
