defmodule EventsService.Events.EventCopier do
  @moduledoc """
  Handles copying of events and all associated relationships.
  """

  import Ecto.Query, warn: false

  alias Adyen.Services.BalancePlatform
  alias Ecto.Multi
  alias EventsService.Accounting.PlatformFee
  alias EventsService.Channels.ChannelConfig
  alias EventsService.Events
  alias EventsService.Events.Artist
  alias EventsService.Events.DonationEvents
  alias EventsService.Events.EntranceArea
  alias EventsService.Events.EntranceAreaTicketCategory
  alias EventsService.Events.EntranceAreaValidationPermission
  alias EventsService.Events.Event
  alias EventsService.Events.EventCopyAssociation
  alias EventsService.Events.EventCounter
  alias EventsService.Events.EventPermission
  alias EventsService.Events.Fee
  alias EventsService.Events.NameShortener
  alias EventsService.Events.SalesChannel
  alias EventsService.Events.TicketCategory
  alias EventsService.Events.Variant
  alias EventsService.Guestlists.Invitation
  alias EventsService.MLP.MultiLevelPricing
  alias EventsService.MLP.MultiLevelPricingModifier
  alias EventsService.MLP.MultiLevelPricingModifierVariant
  alias EventsService.Offers.Availability
  alias EventsService.Pubsub.Publisher.SalesChannelPublisher
  alias EventsService.Pubsub.Publisher.TicketCategoryPublisher
  alias EventsService.Repo
  alias EventsService.SeatComments.SeatCommentGroup
  alias EventsService.Tracking.TrackingPixel
  alias EventsService.Tracking.TrackingPixelCredential
  alias EventsService.Vendor.Promoter
  alias ExServiceClient.Services.AccountsService

  require Logger
  require UUID

  @max_event_title_length 50
  @ellipsis "..."
  @default_exclusions [:id, :__meta__, :__struct__, :inserted_at, :updated_at, :deleted_at]

  @copy_preloads [
    :promoter,
    {:venue, [:geo_coordinate, {:address, [:country]}]},
    :artists,
    :fees,
    :donations,
    :platform_fees,
    {:ticket_categories,
     [
       {:mlp, [modifiers: [:variants]]},
       entrance_area_ticket_categories: :entrance_area
     ]},
    {:variants,
     [
       :availability,
       :ticket_category,
       :mlpm,
       {:sales_channel, :channel_config},
       :predecessor
     ]},
    {:entrance_areas, [:entrance_area_validation_permissions, entrance_area_ticket_categories: :ticket_category]},
    {:tracking_pixels, :tracking_pixel_credentials},
    :seat_comment_groups
    # Not preloading: :permissions (handled separately), :tracking_links (not copied), :event_counter (new)
  ]

  @doc """
  Prepares details needed for a batch copy job by determining the base title
  of the source event and the highest existing copy number for that base title.
  This allows the parent worker to calculate these once for the entire batch.
  Accepts a source_event_id and performs its own fetch and validation.
  """
  @spec prepare_copy_job_details(source_event_id :: Ecto.UUID.t(), repo_module :: module()) ::
          {:ok, %{base_title: String.t(), highest_existing_num: integer()}} | {:error, term()}
  def prepare_copy_job_details(source_event_id, repo_module \\ Repo) do
    with {:ok, source_event} <- fetch_event(source_event_id),
         :ok <- validate_event_seating_plan(source_event) do
      parsed_title = parse_title_for_copying(source_event.title)
      base_title = parsed_title.base_title

      highest_existing_num =
        get_current_highest_copy_number(repo_module, base_title, source_event.promoter_id)

      {:ok, %{base_title: base_title, highest_existing_num: highest_existing_num}}
    end
  end

  @doc """
  Copies an event with all its associations.

  ## Options

    * `:number_of_copies` - the number of copies to create (default: 1)
    * `:copy_options` - list of atoms specifying which associations to copy (default: [])
      Available options: `:availabilities`

  ## Returns

    * `{:ok, id}` - the ID of the first copied event
    * `{:error, :source_event_not_found}` - if the source event cannot be found
    * `{:error, :event_start_date_missing}` - if the source event has no start date
    * `{:error, :event_start_date_not_in_future}` - if the source event's start date is not in the future
    * `{:error, :event_has_seating_plan}` - if the source event has a seating plan
    * `{:error, {:copy_failed, copy_iteration, reason}}` - if copying fails
  """
  @spec copy_event(
          source_event_id :: Ecto.UUID.t(),
          creator_user_id :: String.t(),
          opts :: Keyword.t()
        ) ::
          {:ok, Ecto.UUID.t()} | {:error, term()}
  def copy_event(source_event_id, creator_user_id, opts \\ []) do
    number_of_copies = Keyword.get(opts, :number_of_copies, 1)
    copy_options = Keyword.get(opts, :copy_options, [])

    with {:ok, source_event} <- fetch_event(source_event_id),
         :ok <- validate_event_seating_plan(source_event) do
      Logger.debug("Copy event started for #{source_event_id} by #{creator_user_id} with #{number_of_copies} copies")
      perform_copies(source_event, creator_user_id, number_of_copies, copy_options)
    end
  end

  @doc """
  Copies a single event.

  ## Parameters
    * `source_event_id` - ID of the source event
    * `creator_user_id` - ID of the user creating the copy
    * `copy_index` - Optional index to use for copy naming (default: nil)
    * `base_title_for_all_copies` - Base title to use for all copies
    * `highest_num_at_start_for_base` - Highest copy number at start
    * `copy_options` - List of atoms specifying which associations to copy (default: [])

  ## Returns

    * `{:ok, id}` - the ID of the copied event
    * `{:error, :source_event_not_found}` - if the source event cannot be found
    * `{:error, :event_start_date_missing}` - if the source event has no start date
    * `{:error, :event_start_date_not_in_future}` - if the source event's start date is not in the future
    * `{:error, :event_has_seating_plan}` - if the source event has a seating plan
    * `{:error, reason}` - if copying fails due to other reasons
  """
  @spec copy_single_event(
          source_event_id :: Ecto.UUID.t(),
          creator_user_id :: String.t(),
          copy_index :: integer() | nil,
          base_title_for_all_copies :: String.t(),
          highest_num_at_start_for_base :: integer(),
          copy_options :: list()
        ) ::
          {:ok, Ecto.UUID.t()} | {:error, term()}
  def copy_single_event(
        source_event_id,
        creator_user_id,
        copy_index,
        base_title_for_all_copies,
        highest_num_at_start_for_base,
        copy_options \\ []
      )
      when not is_nil(base_title_for_all_copies) and not is_nil(highest_num_at_start_for_base) do
    with {:ok, source_event} <- fetch_event(source_event_id),
         :ok <- validate_event_seating_plan(source_event) do
      effective_k = if is_nil(copy_index), do: 1, else: copy_index

      Logger.debug(
        "Copying event: source_id=#{source_event_id}, effective_k=#{effective_k}, base_title='#{base_title_for_all_copies}', highest_at_start_for_base=#{highest_num_at_start_for_base}"
      )

      multi =
        copy_single_event_multi(
          source_event,
          creator_user_id,
          effective_k,
          base_title_for_all_copies,
          highest_num_at_start_for_base,
          copy_options
        )

      case Repo.transaction(multi) do
        {:ok, %{new_event: %Event{id: new_event_id}} = result_map} ->
          %EventCopyAssociation{}
          |> EventCopyAssociation.changeset(%{
            source_event_id: source_event_id,
            copied_event_id: new_event_id,
            created_by: creator_user_id
          })
          |> Repo.insert()

          process_successful_transaction(result_map, source_event)
          Logger.info("Successfully copied event #{source_event_id} to new event #{new_event_id}")
          {:ok, new_event_id}

        {:error, failed_op, failed_value, _changes_so_far} ->
          Logger.error("Failed to copy event #{source_event_id}: #{inspect({failed_op, failed_value})}")
          {:error, {:copy_failed, effective_k, {failed_op, failed_value}}}
      end
    else
      {:error, reason} = error ->
        Logger.error("Failed to prepare event for copy #{source_event_id}: #{inspect(reason)}")
        error
    end
  end

  defp fetch_event(source_event_id) do
    case Events.get_event(source_event_id, @copy_preloads) do
      nil -> {:error, :source_event_not_found}
      %Event{} = source_event -> {:ok, source_event}
    end
  end

  defp validate_event_seating_plan(%Event{chart_key: nil}), do: :ok

  defp validate_event_seating_plan(%Event{chart_key: _chart_key}) do
    Logger.info("Attempt to copy event with a seating plan")
    {:error, :event_has_seating_plan}
  end

  defp perform_copies(
         %Event{id: source_event_id, title: source_title, promoter_id: promoter_id} = source_event,
         creator_user_id,
         number_of_copies,
         copy_options
       ) do
    %{base_title: base_title_for_batch} = parse_title_for_copying(source_title)

    highest_num_at_batch_start =
      get_current_highest_copy_number(Repo, base_title_for_batch, promoter_id)

    Enum.reduce_while(1..number_of_copies, {:ok, nil}, fn copy_iteration, {:ok, first_copied_id_acc} ->
      Logger.debug("Starting copy ##{copy_iteration} for event #{source_event_id} by user #{creator_user_id}")

      case copy_single_event(
             source_event.id,
             creator_user_id,
             copy_iteration,
             base_title_for_batch,
             highest_num_at_batch_start,
             copy_options
           ) do
        {:ok, new_event_id} ->
          new_first_id = select_first_id(first_copied_id_acc, new_event_id)
          {:cont, {:ok, new_first_id}}

        {:error, reason} = error ->
          Logger.error("Transaction failed for copy ##{copy_iteration} of event #{source_event_id}: #{inspect(reason)}")
          {:halt, error}
      end
    end)
  end

  defp select_first_id(nil, new_id), do: new_id
  defp select_first_id(existing_id, _), do: existing_id

  defp copy_single_event_multi(
         source_event,
         creator_user_id,
         current_job_copy_index,
         base_title_for_all_copies,
         highest_num_for_base_at_job_start,
         copy_options
       ) do
    source_event
    |> create_base_event_multi(
      creator_user_id,
      current_job_copy_index,
      base_title_for_all_copies,
      highest_num_for_base_at_job_start
    )
    |> add_all_associations(source_event, creator_user_id, copy_options)
  end

  defp process_successful_transaction(%{new_event: %Event{id: new_event_id}} = result_map, %Event{id: source_event_id}) do
    Logger.debug("Successfully copied event #{source_event_id} to new event #{new_event_id}")

    Enum.each(result_map, fn
      {{:new_ticket_category, _original_tc_id}, %TicketCategory{} = new_ticket_category} ->
        case TicketCategoryPublisher.publish_ticket_category_update(new_ticket_category) do
          :ok ->
            Logger.debug(
              "Successfully published ticket category update for copied TC #{new_ticket_category.id} from event copy #{new_event_id}"
            )

          {:error, reason} ->
            Logger.critical(
              "Failed to publish ticket category update for copied TC #{new_ticket_category.id} from event copy #{new_event_id}: #{inspect(reason)}"
            )
        end

      {{:new_sales_channel, _original_sc_id}, %SalesChannel{} = new_sales_channel} ->
        case SalesChannelPublisher.publish_sales_channel_update(new_sales_channel) do
          :ok ->
            Logger.debug(
              "Successfully published sales channel update for copied SC #{new_sales_channel.id} from event copy #{new_event_id}"
            )

          {:error, reason} ->
            Logger.critical(
              "Failed to publish sales channel update for copied SC #{new_sales_channel.id} from event copy #{new_event_id}: #{inspect(reason)}"
            )
        end

      _ ->
        :ok
    end)
  end

  defp create_base_event_multi(
         source_event,
         creator_user_id,
         current_job_copy_index,
         base_title_for_all_copies,
         highest_num_for_base_at_job_start
       ) do
    promoter_id = source_event.promoter_id

    Multi.new()
    |> Multi.run(:calculated_attributes, fn repo, _changes ->
      initial_search_number =
        highest_num_for_base_at_job_start + current_job_copy_index

      unique_title =
        find_next_available_title_starting_from(
          repo,
          base_title_for_all_copies,
          promoter_id,
          initial_search_number
        )

      new_slug =
        Events.get_slug(%Event{
          title: unique_title,
          start_date: source_event.start_date,
          venue: source_event.venue
        })

      {:ok, %{unique_title: unique_title, slug: new_slug}}
    end)
    |> Multi.insert(:new_event, fn changes ->
      %{unique_title: unique_title, slug: new_slug} = changes.calculated_attributes

      new_event_attrs =
        source_event
        |> build_event_attributes(creator_user_id)
        |> Map.put(:title, unique_title)
        |> Map.put(:slug, new_slug)

      Event.changeset(%Event{}, new_event_attrs)
    end)
  end

  defp find_next_available_title_starting_from(repo, base_title, promoter_id, start_number) do
    start_number
    |> Stream.iterate(&(&1 + 1))
    |> Stream.map(&create_copy_title(base_title, &1))
    |> Enum.find(fn title -> !title_exists_in_repo?(repo, title, promoter_id) end)
  end

  defp create_copy_title(base_title, copy_number) do
    suffix = if copy_number == 1, do: " Copy", else: " Copy #{copy_number}"
    max_len = @max_event_title_length - String.length(suffix)
    adjust_base_title_length(base_title, max_len) <> suffix
  end

  defp title_exists_in_repo?(repo, title, promoter_id) do
    query =
      from e in Event,
        where: e.title == ^title and e.promoter_id == ^promoter_id and is_nil(e.deleted_at)

    repo.exists?(query)
  end

  defp add_all_associations(multi, source_event, creator_user_id, copy_options) do
    multi
    |> copy_artists_multi(source_event)
    |> copy_fees_multi(source_event)
    |> copy_donations_multi(source_event)
    |> copy_platform_fees_multi(source_event)
    |> maybe_copy_availabilities_multi(source_event, copy_options)
    |> copy_mlps_multi(source_event)
    |> copy_ticket_categories_multi(source_event)
    |> copy_variants_multi(source_event)
    |> associate_variants_with_mlp_modifiers_multi(source_event)
    |> link_variant_predecessors_multi(source_event)
    |> copy_channel_configs_and_sales_channels_multi(source_event)
    |> copy_entrance_areas_multi(source_event, creator_user_id)
    |> copy_tracking_pixels_multi(source_event)
    |> copy_seat_comment_groups_multi(source_event)
    |> add_creator_permissions_multi(creator_user_id)
    |> add_security_user_permissions_multi()
    |> add_balance_account_multi(source_event)
    |> add_event_counter_multi()
  end

  defp copy_artists_multi(multi, %Event{artists: artists}) do
    Enum.reduce(artists, multi, fn source_artist, acc_multi ->
      Multi.insert(acc_multi, {:new_artist, source_artist.id}, fn %{new_event: %Event{id: new_event_id}} ->
        source_artist
        |> copy_attributes(%{event_id: new_event_id})
        |> then(&Artist.changeset(%Artist{}, &1))
      end)
    end)
  end

  defp copy_fees_multi(multi, %Event{fees: fees}) do
    Enum.reduce(fees, multi, fn source_fee, acc_multi ->
      Multi.insert(acc_multi, {:new_fee, source_fee.id}, fn %{new_event: %Event{id: new_event_id}} ->
        source_fee
        |> copy_attributes(%{event_id: new_event_id})
        |> then(&Fee.changeset(%Fee{}, &1))
      end)
    end)
  end

  defp copy_donations_multi(multi, %Event{donations: donations}) do
    Enum.reduce(donations, multi, fn source_donation, acc_multi ->
      Multi.insert(acc_multi, {:new_donation, source_donation.id}, fn %{new_event: %Event{id: new_event_id}} ->
        source_donation
        |> copy_attributes(%{event_id: new_event_id})
        |> then(&DonationEvents.changeset(%DonationEvents{}, &1))
      end)
    end)
  end

  defp copy_platform_fees_multi(multi, %Event{platform_fees: platform_fees}) do
    Enum.reduce(platform_fees, multi, fn source_platform_fee, acc_multi ->
      Multi.insert(acc_multi, {:new_platform_fee, source_platform_fee.id}, fn %{new_event: %Event{id: new_event_id}} ->
        source_platform_fee
        |> copy_attributes(%{event_id: new_event_id})
        |> then(&PlatformFee.changeset(%PlatformFee{}, &1))
      end)
    end)
  end

  defp maybe_copy_availabilities_multi(multi, %Event{} = source_event, copy_options),
    do: if("availabilities" in copy_options, do: copy_availabilities_multi(multi, source_event), else: multi)

  defp copy_availabilities_multi(multi, %Event{} = source_event) do
    source_event
    |> extract_variants_with_availability_for_copy()
    |> extract_unique_availability_ids_from_variants()
    |> Enum.reduce(multi, fn availability_id, acc_multi ->
      variant_with_this_avail = find_variant_with_original_availability_id(source_event, availability_id)
      add_availability_to_multi(acc_multi, variant_with_this_avail.availability, availability_id)
    end)
  end

  defp extract_variants_with_availability_for_copy(%Event{variants: variants}) do
    Enum.filter(variants, fn %Variant{availability: availability} -> availability end)
  end

  defp extract_unique_availability_ids_from_variants(variants_with_availability) do
    variants_with_availability
    |> Enum.map(fn %Variant{availability_id: availability_id} -> availability_id end)
    |> Enum.uniq()
  end

  defp find_variant_with_original_availability_id(%Event{variants: variants}, original_availability_id) do
    Enum.find(variants, fn %Variant{availability_id: var_avail_id} -> var_avail_id == original_availability_id end)
  end

  # TODO: ONCE WE ALLOW PAST EVENTS, SET AVAILABILITY DATE TO FUTURE SELECTED DATE FROM USER
  defp add_availability_to_multi(
         multi,
         %Availability{
           comment: comment,
           is_only_internal_use: is_only_internal_use,
           name: name,
           valid_from: valid_from,
           valid_until: valid_until
         },
         availability_id
       ) do
    Multi.insert(multi, {:new_availability, availability_id}, fn %{new_event: %Event{id: new_event_id}} ->
      Availability.changeset(%Availability{}, %{
        comment: comment,
        is_only_internal_use: is_only_internal_use,
        name: name,
        valid_from: valid_from,
        valid_until: valid_until,
        event_id: new_event_id
      })
    end)
  end

  defp copy_mlps_multi(multi, %Event{} = source_event) do
    source_event
    |> extract_mlps_from_ticket_categories()
    |> Enum.reduce(multi, fn %MultiLevelPricing{id: mlp_id} = mlp, acc_multi ->
      acc_multi
      |> add_mlp_to_multi(mlp, mlp_id)
      |> add_modifiers_to_multi(extract_modifiers(mlp), mlp_id)
    end)
  end

  defp add_mlp_to_multi(multi, %MultiLevelPricing{name: name, notes: notes}, mlp_id) do
    Multi.insert(multi, {:new_mlp, mlp_id}, fn %{new_event: %Event{id: new_event_id}} ->
      MultiLevelPricing.changeset(%MultiLevelPricing{}, %{
        name: name,
        notes: notes,
        event_id: new_event_id
      })
    end)
  end

  defp add_modifiers_to_multi(multi, modifiers, mlp_id) do
    Enum.reduce(modifiers, multi, fn %MultiLevelPricingModifier{id: modifier_id} = modifier, acc_multi ->
      add_modifier_to_multi(acc_multi, modifier, modifier_id, mlp_id)
    end)
  end

  defp add_modifier_to_multi(multi, modifier, modifier_id, mlp_id) do
    Multi.insert(multi, {:new_mlp_modifier, modifier_id}, fn changes ->
      new_mlp_instance = Map.get(changes, {:new_mlp, mlp_id})
      create_modifier_changeset_with_mlp(modifier, new_mlp_instance)
    end)
  end

  defp create_modifier_changeset_with_mlp(
         %MultiLevelPricingModifier{
           label: label,
           description: description,
           is_default: is_default,
           flat_modifier: flat_modifier,
           percentage_modifier: percentage_modifier,
           min_amount: min_amount,
           max_amount: max_amount,
           order_no: order_no
         },
         %MultiLevelPricing{id: mlp_id}
       ) do
    attrs = %{
      label: label,
      description: description,
      is_default: is_default,
      flat_modifier: flat_modifier,
      percentage_modifier: percentage_modifier,
      min_amount: min_amount,
      max_amount: max_amount,
      mlp_id: mlp_id,
      order_no: order_no
    }

    MultiLevelPricingModifier.changeset(%MultiLevelPricingModifier{}, attrs)
  end

  defp create_modifier_changeset_with_mlp(_modifier, nil),
    do: %Ecto.Changeset{valid?: false, errors: [mlp_id: {"MLP not found", []}]}

  defp copy_ticket_categories_multi(multi, source_event) do
    Enum.reduce(source_event.ticket_categories, multi, fn tc, acc_multi ->
      guestlist_quota_sum = sum_guestlist_quota_for_ticket_category(tc, source_event.variants)

      adjusted_quota = max(0, (tc.quota || 0) - guestlist_quota_sum)
      adjusted_reserved_quota = max(0, (tc.reserved_quota || 0) - guestlist_quota_sum)

      Multi.insert(acc_multi, {:new_ticket_category, tc.id}, fn changes ->
        new_event_id = changes.new_event.id
        new_mlp_id = get_new_mlp_id(changes, tc.mlp_id)

        TicketCategory.changeset(%TicketCategory{}, %{
          name: tc.name,
          description: tc.description,
          tax_rate: tc.tax_rate,
          event_id: new_event_id,
          is_visible: tc.is_visible,
          # needs to be adjusted when copying seat events
          external_id: tc.external_id,
          external_provider: tc.external_provider,
          admission: tc.admission,
          hint: tc.hint,
          ticket_type: tc.ticket_type,
          quota: adjusted_quota,
          reserved_quota: adjusted_reserved_quota,
          price: tc.price,
          mlp_id: new_mlp_id,
          min_amount: tc.min_amount,
          max_amount: tc.max_amount
        })
      end)
    end)
  end

  defp sum_guestlist_quota_for_ticket_category(%TicketCategory{id: ticket_category_id}, all_variants_from_source_event) do
    variant_ids_for_tc =
      all_variants_from_source_event
      |> Enum.filter(&(&1.ticket_category_id == ticket_category_id))
      |> Enum.map(& &1.id)

    if Enum.empty?(variant_ids_for_tc) do
      0
    else
      query =
        from i in Invitation,
          where: i.variant_id in ^variant_ids_for_tc,
          select: sum(i.quota)

      Repo.one(query) || 0
    end
  end

  defp get_new_mlp_id(changes, mlp_id) do
    changes
    |> Map.get({:new_mlp, mlp_id})
    |> extract_id()
  end

  defp extract_id(nil), do: nil
  defp extract_id(value), do: value.id

  defp copy_variants_multi(multi, source_event) do
    Enum.reduce(source_event.variants, multi, fn variant, acc_multi ->
      Multi.insert(acc_multi, {:new_variant, variant.id}, fn changes ->
        new_event_id = changes.new_event.id
        new_ticket_category_id = changes[{:new_ticket_category, variant.ticket_category_id}].id
        new_availability_id = get_new_availability_id(changes, variant.availability_id)

        Variant.changeset(%Variant{}, %{
          event_id: new_event_id,
          ticket_category_id: new_ticket_category_id,
          availability_id: new_availability_id,
          unit_price: variant.unit_price,
          quota: variant.quota,
          is_default: variant.is_default,
          is_sold_out_from_reservation: variant.is_sold_out_from_reservation,
          is_visible: variant.is_visible,
          order_no: variant.order_no,
          presale_start_date: variant.presale_start_date,
          visibility_after_sales_ended: variant.visibility_after_sales_ended,
          visibility_before_sales_started: variant.visibility_before_sales_started,
          min_amount: variant.min_amount,
          max_amount: variant.max_amount,
          distribution_type: variant.distribution_type,
          predecessor_id: nil
        })
      end)
    end)
  end

  defp get_new_availability_id(changes, availability_id) do
    changes
    |> Map.get({:new_availability, availability_id})
    |> extract_id()
  end

  defp link_variant_predecessors_multi(multi, %Event{variants: variants}) do
    variants
    |> Enum.filter(& &1.predecessor_id)
    |> Enum.reduce(multi, &add_predecessor_link/2)
  end

  defp add_predecessor_link(source_variant, acc_multi) do
    Multi.update(acc_multi, {:link_predecessor, source_variant.id}, fn changes ->
      case {Map.get(changes, {:new_variant, source_variant.id}),
            Map.get(changes, {:new_variant, source_variant.predecessor_id})} do
        {new_variant, new_predecessor_variant} when not is_nil(new_variant) and not is_nil(new_predecessor_variant) ->
          Variant.changeset(new_variant, %{predecessor_id: new_predecessor_variant.id})

        {new_variant, _} when not is_nil(new_variant) ->
          Logger.warning("Could not link predecessor for new variant based on source variant #{source_variant.id}")
          Ecto.Changeset.change(new_variant)

        _ ->
          Logger.error("Missing new variant when linking predecessor for source variant #{source_variant.id}")
          %Ecto.Changeset{valid?: false, errors: [base: {"Missing variant", []}]}
      end
    end)
  end

  defp associate_variants_with_mlp_modifiers_multi(multi, source_event) do
    source_event
    |> extract_mlps_from_ticket_categories()
    |> Enum.reduce(multi, fn mlp, acc_multi ->
      mlp
      |> extract_modifiers()
      |> Enum.reduce(acc_multi, &associate_modifier_with_variants(&1, &2, source_event))
    end)
  end

  defp extract_mlps_from_ticket_categories(%Event{ticket_categories: ticket_categories})
       when is_list(ticket_categories) do
    ticket_categories
    |> Enum.map(fn %TicketCategory{mlp: mlp} -> mlp end)
    |> Enum.reject(&is_nil/1)
    |> Enum.uniq_by(fn %MultiLevelPricing{id: id} -> id end)
  end

  defp extract_mlps_from_ticket_categories(_), do: []

  defp extract_modifiers(%MultiLevelPricing{modifiers: modifiers}) when is_list(modifiers) do
    Enum.reject(modifiers, &is_nil/1)
  end

  defp extract_modifiers(_), do: []

  defp associate_modifier_with_variants(modifier, acc_multi, source_event) do
    modifier_id = Map.get(modifier, :id)

    if is_nil(modifier_id) do
      Logger.warning("Skipping association for modifier with nil ID")
      acc_multi
    else
      source_variants = find_variants_with_modifier(source_event, modifier_id)

      Enum.reduce(source_variants, acc_multi, fn source_variant, multi ->
        add_mlp_variant_association(multi, modifier, source_variant)
      end)
    end
  end

  defp find_variants_with_modifier(%Event{variants: variants}, modifier_id) do
    Enum.filter(variants, fn variant ->
      variant
      |> Map.get(:mlpm, [])
      |> Enum.any?(fn modifier_assoc -> Map.get(modifier_assoc, :id) == modifier_id end)
    end)
  end

  defp add_mlp_variant_association(multi, modifier, source_variant) do
    modifier_id = Map.get(modifier, :id)
    variant_id = Map.get(source_variant, :id)

    Multi.insert(multi, {:new_mlpm_variant_assoc, UUID.uuid4()}, fn changes ->
      new_modifier = Map.get(changes, {:new_mlp_modifier, modifier_id})
      new_variant = Map.get(changes, {:new_variant, variant_id})

      create_mlp_variant_assoc_changeset(new_modifier, new_variant, modifier, source_variant)
    end)
  end

  defp create_mlp_variant_assoc_changeset(nil, _, modifier, source_variant) do
    log_missing_entities(modifier, source_variant)
    create_invalid_mlp_variant_changeset()
  end

  defp create_mlp_variant_assoc_changeset(_, nil, modifier, source_variant) do
    log_missing_entities(modifier, source_variant)
    create_invalid_mlp_variant_changeset()
  end

  defp create_mlp_variant_assoc_changeset(new_modifier, new_variant, _, _) do
    create_mlp_variant_changeset(new_modifier.id, new_variant.id)
  end

  defp create_mlp_variant_changeset(modifier_id, variant_id) do
    MultiLevelPricingModifierVariant.changeset(
      %MultiLevelPricingModifierVariant{},
      %{mlpm_id: modifier_id, variant_id: variant_id}
    )
  end

  defp create_invalid_mlp_variant_changeset do
    %MultiLevelPricingModifierVariant{}
    |> MultiLevelPricingModifierVariant.changeset(%{})
    |> Ecto.Changeset.add_error(:base, "Missing entities for association")
  end

  defp log_missing_entities(modifier, source_variant) do
    Logger.error(
      "Missing new_modifier or new_variant for association. " <>
        "Modifier: #{Map.get(modifier, :id)}, Variant: #{Map.get(source_variant, :id)}"
    )
  end

  defp copy_channel_configs_and_sales_channels_multi(multi, source_event) do
    multi
    |> create_channel_configs(source_event)
    |> create_sales_channels(source_event)
  end

  defp create_channel_configs(multi, source_event) do
    source_event
    |> extract_unique_channel_configs()
    |> Enum.reduce(multi, fn config, acc_multi ->
      add_channel_config_to_multi(acc_multi, config)
    end)
  end

  defp extract_unique_channel_configs(%Event{variants: variants}) do
    variants
    |> Enum.flat_map(fn variant ->
      variant
      |> Map.get(:sales_channel)
      |> extract_channel_config()
    end)
    |> Enum.uniq_by(&Map.get(&1, :id))
  end

  defp extract_channel_config(nil), do: []
  defp extract_channel_config(%{channel_config: config}) when not is_nil(config), do: [config]
  defp extract_channel_config(_), do: []

  defp add_channel_config_to_multi(multi, config) do
    config_id = Map.get(config, :id)

    Multi.insert(multi, {:new_channel_config, config_id}, fn %{new_event: new_event_shell} ->
      ChannelConfig.changeset(%ChannelConfig{}, %{
        event_id: new_event_shell.id,
        token: EventsService.Util.Token.generate_token(),
        channel_key: Ecto.UUID.generate(),
        type: Map.get(config, :type),
        value: Map.get(config, :value),
        label: Map.get(config, :label),
        description: Map.get(config, :description),
        color: Map.get(config, :color),
        amount_of_objects: Map.get(config, :amount_of_objects),
        valid_until: Map.get(config, :valid_until)
      })
    end)
  end

  defp create_sales_channels(multi, %Event{variants: variants}) do
    variants
    |> Enum.filter(&Map.get(&1, :sales_channel))
    |> Enum.reduce(multi, fn variant, acc_multi ->
      create_sales_channel(acc_multi, variant, variant.sales_channel)
    end)
  end

  defp create_sales_channel(multi, variant, sales_channel) do
    sc_id = Map.get(sales_channel, :id)
    variant_id = Map.get(variant, :id)
    channel_config = Map.get(sales_channel, :channel_config)
    channel_config_id = Map.get(channel_config, :id)

    Multi.insert(multi, {:new_sales_channel, sc_id}, fn changes ->
      with %{id: new_variant_id} <- Map.get(changes, {:new_variant, variant_id}),
           %{id: new_config_id} <- Map.get(changes, {:new_channel_config, channel_config_id}) do
        create_sales_channel_changeset(new_variant_id, new_config_id, sales_channel)
      else
        nil ->
          %Ecto.Changeset{valid?: false, errors: [base: {"Missing variant or channel config", []}]}
      end
    end)
  end

  defp create_sales_channel_changeset(variant_id, config_id, sales_channel) do
    SalesChannel.changeset(%SalesChannel{}, %{
      variant_id: variant_id,
      channel_config_id: config_id,
      quota_mode: Map.get(sales_channel, :quota_mode),
      original_price: Map.get(sales_channel, :original_price)
    })
  end

  defp copy_entrance_areas_multi(multi, source_event, _creator_user_id) do
    Enum.reduce(source_event.entrance_areas, multi, fn entrance_area, acc_multi ->
      acc_multi
      |> create_security_user_for_entrance_area(entrance_area)
      |> create_entrance_area(entrance_area)
      |> create_ticket_category_links(entrance_area)
      |> create_validation_permission(entrance_area)
      |> create_event_permission_for_ea_security_user(entrance_area)
    end)
  end

  defp create_security_user_for_entrance_area(multi, entrance_area) do
    Multi.run(multi, {:new_security_user, entrance_area.id}, fn _repo, _changes ->
      create_and_handle_security_user()
    end)
  end

  defp create_and_handle_security_user do
    case AccountsService.create_security_user() do
      {:ok, %{"id" => sec_user_id}} ->
        {:ok, sec_user_id}

      error ->
        Logger.error("Failed to create security user for EA copy: #{inspect(error)}")
        {:error, :security_user_creation_failed}
    end
  end

  defp create_entrance_area(multi, entrance_area) do
    Multi.insert(multi, {:new_entrance_area, entrance_area.id}, fn changes ->
      new_event_id = changes.new_event.id

      case Map.get(changes, {:new_security_user, entrance_area.id}) do
        security_user_id when not is_nil(security_user_id) ->
          create_entrance_area_changeset(entrance_area, new_event_id, security_user_id)

        _ ->
          %Ecto.Changeset{valid?: false, errors: [base: {"Missing security user", []}]}
      end
    end)
  end

  defp create_entrance_area_changeset(entrance_area, event_id, security_user_id) do
    EntranceArea.changeset(%EntranceArea{}, %{
      name: entrance_area.name,
      description: entrance_area.description,
      event_id: event_id,
      user_document_id: security_user_id
    })
  end

  defp create_ticket_category_links(multi, entrance_area) do
    Enum.reduce(entrance_area.entrance_area_ticket_categories, multi, fn ea_tc, acc_multi ->
      create_entrance_area_ticket_category(acc_multi, ea_tc, entrance_area)
    end)
  end

  defp create_entrance_area_ticket_category(multi, ea_tc, entrance_area) do
    Multi.insert(multi, {:new_eatc, ea_tc.ticket_category_id, entrance_area.id}, fn changes ->
      new_ea = Map.get(changes, {:new_entrance_area, entrance_area.id})
      new_tc = Map.get(changes, {:new_ticket_category, ea_tc.ticket_category_id})

      create_entrance_area_ticket_category_changeset(new_ea, new_tc)
    end)
  end

  defp create_entrance_area_ticket_category_changeset(nil, _) do
    %Ecto.Changeset{valid?: false, errors: [entrance_area_id: {"Entrance area not found", []}]}
  end

  defp create_entrance_area_ticket_category_changeset(_, nil) do
    %Ecto.Changeset{valid?: false, errors: [ticket_category_id: {"Ticket category not found", []}]}
  end

  defp create_entrance_area_ticket_category_changeset(ea_instance, tc_instance) do
    EntranceAreaTicketCategory.changeset(%EntranceAreaTicketCategory{}, %{
      entrance_area_id: ea_instance.id,
      ticket_category_id: tc_instance.id
    })
  end

  defp create_validation_permission(multi, entrance_area) do
    Multi.insert(multi, {:new_eavp, entrance_area.id}, fn changes ->
      with %{id: ea_id} <- Map.get(changes, {:new_entrance_area, entrance_area.id}),
           security_user_id when not is_nil(security_user_id) <-
             Map.get(changes, {:new_security_user, entrance_area.id}) do
        create_validation_permission_changeset(ea_id, security_user_id)
      else
        _ ->
          %Ecto.Changeset{valid?: false, errors: [base: {"Missing entrance area or security user", []}]}
      end
    end)
  end

  defp create_validation_permission_changeset(entrance_area_id, security_user_id) do
    EntranceAreaValidationPermission.changeset(%EntranceAreaValidationPermission{}, %{
      entrance_area_id: entrance_area_id,
      user_document_id: security_user_id
    })
  end

  defp create_event_permission_for_ea_security_user(multi, entrance_area) do
    Multi.insert(multi, {:new_ea_event_permission, entrance_area.id}, fn changes ->
      new_event_id = changes.new_event.id

      case Map.get(changes, {:new_security_user, entrance_area.id}) do
        security_user_id when not is_nil(security_user_id) ->
          EventPermission.changeset(%EventPermission{}, %{
            event_id: new_event_id,
            role: [:SECURITY],
            user_document_id: security_user_id,
            is_system_user: true
          })

        _ ->
          %Ecto.Changeset{valid?: false, errors: [base: {"Missing security user for EA event permission", []}]}
      end
    end)
  end

  defp copy_tracking_pixels_multi(multi, source_event) do
    Enum.reduce(source_event.tracking_pixels, multi, fn tp, acc_multi ->
      acc_multi
      |> insert_tracking_pixel(tp)
      |> add_tracking_pixel_credentials(tp)
    end)
  end

  defp insert_tracking_pixel(multi, tracking_pixel) do
    Multi.insert(multi, {:new_tracking_pixel, tracking_pixel.id}, fn %{new_event: new_event_shell} ->
      TrackingPixel.changeset(%TrackingPixel{}, %{
        label: tracking_pixel.label,
        type: tracking_pixel.type,
        event_id: new_event_shell.id
      })
    end)
  end

  defp add_tracking_pixel_credentials(multi, tracking_pixel) do
    Enum.reduce(tracking_pixel.tracking_pixel_credentials, multi, fn credential, acc_multi ->
      insert_tracking_pixel_credential(acc_multi, credential, tracking_pixel.id)
    end)
  end

  defp insert_tracking_pixel_credential(multi, credential, tracking_pixel_id) do
    Multi.insert(multi, {:new_tp_credential, credential.id}, fn changes ->
      new_tp_instance = Map.get(changes, {:new_tracking_pixel, tracking_pixel_id})
      create_credential_changeset(new_tp_instance, credential)
    end)
  end

  defp create_credential_changeset(nil, _credential) do
    %Ecto.Changeset{valid?: false, errors: [tracking_pixel_id: {"Tracking pixel not found", []}]}
  end

  defp create_credential_changeset(new_tp_instance, credential) do
    TrackingPixelCredential.changeset(%TrackingPixelCredential{}, %{
      tracking_pixel_id: new_tp_instance.id,
      type: credential.type,
      value: credential.value
    })
  end

  defp copy_seat_comment_groups_multi(multi, source_event) do
    Enum.reduce(source_event.seat_comment_groups, multi, fn scg, acc_multi ->
      Multi.insert(acc_multi, {:new_seat_comment_group, scg.id}, fn %{new_event: new_event_shell} ->
        SeatCommentGroup.changeset(%SeatCommentGroup{}, %{
          group_key: scg.group_key,
          created_by_document_id: new_event_shell.created_by_document_id,
          name: scg.name,
          event_id: new_event_shell.id
        })
      end)
    end)
  end

  defp add_creator_permissions_multi(multi, creator_user_id) do
    Multi.insert(multi, :creator_permission, fn %{new_event: new_event_shell} ->
      attrs = %{
        event_id: new_event_shell.id,
        role: [:EVENT_ADMIN],
        user_document_id: creator_user_id
      }

      EventPermission.changeset(%EventPermission{}, attrs)
    end)
  end

  defp add_security_user_permissions_multi(multi) do
    multi
    |> Multi.run(:security_user, fn _repo, _changes ->
      case AccountsService.create_security_user() do
        {:ok, security} -> {:ok, security}
        _ -> {:error, :security_user_creation_failed}
      end
    end)
    |> Multi.insert(:security_user_permission, fn changes ->
      new_event_shell = changes.new_event
      security_user = changes.security_user

      attrs = %{
        event_id: new_event_shell.id,
        role: [:SECURITY],
        user_document_id: security_user["id"],
        is_system_user: true
      }

      EventPermission.changeset(%EventPermission{}, attrs)
    end)
  end

  defp add_balance_account_multi(multi, source_event) do
    multi
    |> Multi.run(:balance_account, fn _repo, changes ->
      handle_balance_account_creation(changes, source_event)
    end)
    |> Multi.update(:update_event_with_balance, fn changes ->
      update_event_balance(changes)
    end)
  end

  defp handle_balance_account_creation(%{new_event: event}, %Event{use_event_balance_account: true} = source_event) do
    create_balance_account_for_event(event, source_event)
  end

  defp handle_balance_account_creation(_changes, %Event{use_event_balance_account: false}) do
    {:ok, nil}
  end

  defp update_event_balance(%{balance_account: %{"id" => balance_account_id}, new_event: event}) do
    Event.changeset(event, %{"balance_account_id" => balance_account_id})
  end

  defp update_event_balance(%{balance_account: nil, new_event: event}) do
    Ecto.Changeset.change(event)
  end

  defp create_balance_account_for_event(%Event{id: id, title: title, start_date: start_date}, %Event{
         promoter: %Promoter{} = promoter
       }) do
    with %Promoter{account_holder_id: account_holder_id} <- promoter do
      payload = %{
        account_holder_id: account_holder_id,
        reference: id,
        description: "#{title} - #{start_date}"
      }

      BalancePlatform.create_balance_account(payload)
    end
  end

  defp add_event_counter_multi(multi) do
    Multi.insert(multi, :event_counter, fn %{new_event: %Event{id: new_event_id}} ->
      EventCounter.changeset(%EventCounter{}, %{event_id: new_event_id, last_sync_at: DateTime.utc_now()})
    end)
  end

  defp build_event_attributes(%Event{} = source_event, creator_user_id) do
    parsed = parse_title_for_copying(source_event.title)
    base_title = parsed.base_title

    # styler:sort
    %{
      admission_date: source_event.admission_date,
      box_office_opening_date: source_event.box_office_opening_date,
      category: source_event.category,
      chart_key: source_event.chart_key,
      closed_at: nil,
      closed_by: nil,
      closed_by_document_id: nil,
      cover_url: source_event.cover_url,
      created_by_document_id: creator_user_id,
      deleted_at: nil,
      description: source_event.description,
      end_date: source_event.end_date,
      firestore_id: nil,
      is_approved: false,
      is_draft: true,
      is_future_demand: false,
      is_visible: false,
      kickback: source_event.kickback,
      plan_id: source_event.plan_id,
      promoter_id: source_event.promoter_id,
      published_date: nil,
      short_code: NameShortener.generate_unique_shortcode(),
      slug: Events.get_slug(%Event{title: base_title, start_date: source_event.start_date, venue: source_event.venue}),
      start_date: source_event.start_date,
      subtitle: source_event.subtitle,
      thumbnail_url: source_event.thumbnail_url,
      ticket_color: source_event.ticket_color,
      title: base_title,
      use_event_balance_account: source_event.use_event_balance_account,
      venue_id: source_event.venue_id
    }
  end

  defp adjust_base_title_length(title, max_len) do
    ellipsis_len = String.length(@ellipsis)

    cond do
      max_len <= 0 ->
        ""

      max_len < ellipsis_len ->
        String.slice(title, 0, max_len)

      String.length(title) > max_len ->
        String.slice(title, 0, max_len - ellipsis_len) <> @ellipsis

      true ->
        title
    end
  end

  defp parse_title_for_copying(title_string) do
    base_title =
      cond do
        String.match?(title_string, ~r" Copy \d+$") ->
          title_string |> String.split(" Copy ") |> List.first()

        String.ends_with?(title_string, " Copy") ->
          String.trim_trailing(title_string, " Copy")

        true ->
          title_string
      end

    original_copy_number =
      cond do
        Regex.match?(~r" Copy (\d+)$", title_string) ->
          [[_, num_str]] = Regex.scan(~r" Copy (\d+)$", title_string)
          String.to_integer(num_str)

        String.ends_with?(title_string, " Copy") ->
          1

        true ->
          0
      end

    %{base_title: base_title, original_copy_number: original_copy_number}
  end

  defp get_current_highest_copy_number(repo, base_title, promoter_id) do
    query =
      from e in Event,
        where: e.promoter_id == ^promoter_id and is_nil(e.deleted_at),
        select: e.title

    titles = repo.all(query)

    numbers =
      titles
      |> Enum.map(&parse_title_for_copying(&1))
      |> Enum.filter(fn parsed -> parsed.base_title == base_title && parsed.original_copy_number > 0 end)
      |> Enum.map(& &1.original_copy_number)

    if Enum.empty?(numbers) do
      0
    else
      Enum.max(numbers)
    end
  end

  defp copy_attributes(source_struct, overrides) do
    source_struct
    |> Map.from_struct()
    |> Map.drop(@default_exclusions)
    |> Map.merge(overrides)
  end
end
