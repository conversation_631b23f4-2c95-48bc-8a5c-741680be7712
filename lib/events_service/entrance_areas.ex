defmodule EventsService.EntranceAreas do
  @moduledoc false

  import Ecto.Query

  alias Ecto.Multi
  alias EventsService.Events.EntranceArea
  alias EventsService.Events.EntranceAreaTicketCategory
  alias EventsService.Events.EntranceAreaValidationPermission
  alias EventsService.Events.Event
  alias EventsService.Events.EventPermission
  alias EventsService.Repo
  alias EventsService.Token
  alias ExServiceClient.Services.AccountsService
  alias ExServiceClient.Services.OrdersService.TicketHistoryRequests

  require Logger

  @spec get_entrance_area(Ecto.UUID.t()) :: EntranceArea.t() | nil
  @spec get_entrance_area(Ecto.UUID.t(), list()) :: EntranceArea.t() | nil
  def get_entrance_area(id, preloads \\ []) do
    query =
      base_query()
      |> where([ea], ea.id == ^id)
      |> preload(^preloads)

    Repo.one(query)
  end

  @spec get_extended_entrance_area(Ecto.UUID.t()) :: EntranceArea.t() | nil
  @spec get_extended_entrance_area(Ecto.UUID.t(), list()) :: EntranceArea.t() | nil
  def get_extended_entrance_area(id, preloads \\ []) do
    query =
      extended_base_query()
      |> where([ea], ea.id == ^id)
      |> preload(^preloads)

    query
    |> Repo.one()
    |> maybe_attach_auth_token()
  end

  @spec paginate_entrance_areas(map() | keyword()) :: Scrivener.Page.t()
  @spec paginate_entrance_areas(map() | keyword(), list()) :: Scrivener.Page.t()
  def paginate_entrance_areas(params, preloads \\ []) do
    query =
      extended_base_query()
      |> where(^filter_where_params(params))
      |> order_by([ea], asc: ea.name)
      |> preload(^preloads)

    # Count total_entries separately to avoid issues with group_by clause
    # This ensures accurate pagination when the main query uses aggregations
    total_entries =
      base_query()
      |> where(^filter_where_params(params))
      |> select([ea], count(ea.id))
      |> Repo.one()

    params = Map.put(params, :options, %{total_entries: total_entries})

    %{entries: entries} = page = Repo.paginate(query, params)

    %{page | entries: Enum.map(entries, &maybe_attach_auth_token/1)}
  end

  @spec get_all_entrance_areas(map() | keyword()) :: list()
  @spec get_all_entrance_areas(map() | keyword(), list()) :: list()
  def get_all_entrance_areas(params, preloads \\ []) do
    query =
      extended_base_query()
      |> where(^filter_where_params(params))
      |> order_by([ea], asc: ea.name)
      |> preload(^preloads)

    query
    |> Repo.all()
    |> Enum.map(&maybe_attach_auth_token/1)
  end

  @spec create_entrance_area(map()) ::
          {:ok, EntranceArea.t()} | {:error, atom()} | {:error, any(), any(), any()} | Ecto.Multi.failure()
  def create_entrance_area(params) do
    case AccountsService.create_security_user() do
      {:ok, %{"id" => security_user_id}} ->
        user_id_field = get_user_field(security_user_id)

        params = Map.put(params, user_id_field, security_user_id)

        Multi.new()
        |> Multi.insert(:insert_entrance_area, EntranceArea.changeset(%EntranceArea{}, params))
        |> Multi.insert(:insert_entrance_area_validation_permission, fn %{insert_entrance_area: entrance_area} ->
          EntranceAreaValidationPermission.changeset(%EntranceAreaValidationPermission{}, %{
            user_id_field => security_user_id,
            :entrance_area_id => entrance_area.id
          })
        end)
        |> maybe_insert_entrance_area_ticket_categories(params[:ticket_category_ids])
        |> insert_security_user_event_permission(params[:event_id], security_user_id)
        |> Repo.transaction()
        |> handle_create_transaction_result()

      {:ok, data} ->
        Logger.error("Failed to create security user because of an invalid response: #{inspect(data)}")
        {:error, :security_user_creation_failed}

      {:error, msg} ->
        Logger.error("Failed to create security user for entrance area: #{inspect(msg)}")
        {:error, :security_user_creation_failed}
    end
  end

  @spec update_entrance_area(EntranceArea.t(), map()) ::
          {:ok, any()} | {:error, any(), any(), any()} | Ecto.Multi.failure()
  def update_entrance_area(%EntranceArea{id: entrance_area_id} = entrance_area, params) do
    Multi.new()
    |> Multi.update(:update_entrance_area, EntranceArea.update_changeset(entrance_area, params))
    |> maybe_replace_entrance_area_ticket_categories(entrance_area_id, params)
    |> Repo.transaction()
  end

  @spec delete_entrance_area(EntranceArea.t()) :: {:ok, EntranceArea.t()} | {:error, Ecto.Changeset.t()}
  def delete_entrance_area(entrance_area) do
    entrance_area
    |> EntranceArea.delete_changeset()
    |> Repo.update()
  end

  @spec get(Ecto.UUID.t()) :: EntranceArea.t() | nil
  @spec get(Ecto.UUID.t(), keyword()) :: EntranceArea.t() | nil
  def get(id, preloads \\ []) do
    query =
      base_query()
      |> where([ea], ea.id == ^id)
      |> preload(^preloads)

    Repo.one(query)
  end

  @spec tickets_used_for_entrance_area_id?(Ecto.UUID.t()) :: {:ok, boolean()} | {:error, any()}
  def tickets_used_for_entrance_area_id?(entrance_area_id) do
    [location_type: :ENTRANCE_AREA, location_id: entrance_area_id]
    |> TicketHistoryRequests.count()
    |> case do
      {:ok, %{"count" => count}} ->
        {:ok, count > 0}

      {:error, error} ->
        Logger.error(
          "Failed to get used ticket count for entrance area id #{inspect(entrance_area_id)}: #{inspect(error)}"
        )

        {:error, error}
    end
  end

  defp base_query do
    from(ea in EntranceArea,
      where: is_nil(ea.deleted_at)
    )
  end

  defp extended_base_query do
    from(ea in EntranceArea,
      left_join: eatc in assoc(ea, :entrance_area_ticket_categories),
      left_join: tc in assoc(eatc, :ticket_category),
      group_by: ea.id,
      select: %{
        ea
        | ticket_category_count: fragment("COUNT(DISTINCT ?)", tc.id),
          ticket_category_contingent: sum(tc.quota)
      },
      where: is_nil(ea.deleted_at) and is_nil(tc.deleted_at)
    )
  end

  defp filter_where_params(params) do
    Enum.reduce(params, dynamic(true), fn {key, value}, acc -> filter_where(acc, key, value) end)
  end

  defp filter_where(dynamic, :event_id, value), do: dynamic([ea], ^dynamic and ea.event_id == ^value)

  defp filter_where(dynamic, :user_document_id, value) do
    subquery =
      from(eavp in EntranceAreaValidationPermission,
        where: eavp.user_document_id == ^value,
        select: eavp.entrance_area_id
      )

    dynamic([ea], ^dynamic and (not exists(subquery) or ea.id in subquery(subquery)))
  end

  defp filter_where(dynamic, :user_id, value) do
    subquery =
      from(eavp in EntranceAreaValidationPermission,
        where: eavp.user_id == ^value,
        select: eavp.entrance_area_id
      )

    dynamic([ea], ^dynamic and (not exists(subquery) or ea.id in subquery(subquery)))
  end

  defp filter_where(dynamic, :ticket_category_id, value) do
    # Filter entrance areas that have a specific ticket category assigned,
    # without affecting the aggregated counts of all their ticket categories

    subquery =
      from(eatc in EntranceAreaTicketCategory,
        where: eatc.ticket_category_id == ^value,
        select: eatc.entrance_area_id
      )

    dynamic([ea], ^dynamic and ea.id in subquery(subquery))
  end

  defp filter_where(dynamic, _key, _value), do: dynamic

  defp maybe_replace_entrance_area_ticket_categories(multi, entrance_area_id, %{
         ticket_category_ids: ticket_category_ids
       })
       when is_list(ticket_category_ids) do
    multi
    |> Multi.delete_all(:delete_entrance_area_ticket_categories, fn _multi ->
      from(eatc in EntranceAreaTicketCategory,
        where: eatc.entrance_area_id == ^entrance_area_id
      )
    end)
    |> maybe_insert_entrance_area_ticket_categories(ticket_category_ids, entrance_area_id)
  end

  defp maybe_replace_entrance_area_ticket_categories(multi, _entrance_area_id, _params), do: multi

  defp maybe_insert_entrance_area_ticket_categories(multi, ticket_category_ids, entrance_area_id \\ nil)

  defp maybe_insert_entrance_area_ticket_categories(multi, ticket_category_ids, entrance_area_id)
       when is_list(ticket_category_ids) do
    ticket_category_ids
    |> Enum.with_index()
    |> Enum.reduce(multi, fn {ticket_category_id, index}, multi ->
      Multi.insert(multi, {:insert_entrance_area_ticket_category, index}, fn multi ->
        entrance_area_id = entrance_area_id || multi.insert_entrance_area.id

        EntranceAreaTicketCategory.changeset(%EntranceAreaTicketCategory{}, %{
          entrance_area_id: entrance_area_id,
          ticket_category_id: ticket_category_id
        })
      end)
    end)
  end

  defp maybe_insert_entrance_area_ticket_categories(multi, _ticket_category_ids, _entrance_area_id), do: multi

  defp insert_security_user_event_permission(multi, event_id, security_user_id) do
    user_field = get_user_field(security_user_id)

    Multi.insert(multi, :insert_event_permission, fn _multi ->
      event_permission_attrs = %{
        :role => [:SECURITY],
        :event_id => event_id,
        :is_system_user => true,
        user_field => security_user_id
      }

      EventPermission.changeset(%EventPermission{}, event_permission_attrs)
    end)
  end

  defp maybe_attach_auth_token(%EntranceArea{event: %Event{} = event} = entrance_area) do
    %Event{start_date: start_date, end_date: end_date} = event
    %EntranceArea{user_document_id: user_document_id, user_id: user_id} = entrance_area

    user_id = user_document_id || user_id

    case Token.create_custom_token(start_date, end_date, user_id) do
      {:ok, token, _data} ->
        %{entrance_area | auth_token: token}

      error ->
        Logger.critical("Failed to generate auth token for entrance area because of error #{inspect(error)}")
        entrance_area
    end
  end

  defp maybe_attach_auth_token(entrance_area), do: entrance_area

  defp handle_create_transaction_result({:ok, %{insert_entrance_area: entrance_area}}), do: {:ok, entrance_area}
  defp handle_create_transaction_result(error), do: error

  defp get_user_field(user_id) do
    case Ecto.UUID.cast(user_id) do
      {:ok, _user_id} -> :user_id
      :error -> :user_document_id
    end
  end
end
