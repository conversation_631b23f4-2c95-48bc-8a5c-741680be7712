defmodule EventsService.Repo.Migrations.AddIsSystemUserToEventPermissions do
  use Ecto.Migration

  def change do
    alter table(:event_permissions) do
      add_if_not_exists :is_system_user, :boolean, default: false, null: false
    end

    # Add index for efficient querying of system users
    create_if_not_exists index(:event_permissions, [:is_system_user])

    # Add composite index for the most common query pattern
    create_if_not_exists index(:event_permissions, [:event_id, :is_system_user])
  end
end
