# System User Migration Implementation

## Overview

This document describes the implementation of the `is_system_user` field in the `event_permissions` table to enable proper identification of system-generated security accounts and facilitate the migration from string-based `user_document_id` to UUID `user_id`.

## Problem Statement

The system previously relied on a brittle identification method for system-generated security accounts using the `sec_` prefix in `user_document_id`. This approach:

1. Prevented migration to standard UUID format for user identification
2. Made it difficult to distinguish between system-generated accounts and human users with SECURITY role
3. Created tight coupling between user identification and naming conventions

## Solution

### Database Schema Changes

Added `is_system_user` boolean field to the `event_permissions` table:

```sql
ALTER TABLE events.event_permissions 
ADD COLUMN is_system_user BOOLEAN DEFAULT FALSE NOT NULL;

-- Indexes for efficient querying
CREATE INDEX idx_event_permissions_is_system_user 
ON events.event_permissions (is_system_user);

CREATE INDEX idx_event_permissions_event_id_is_system_user 
ON events.event_permissions (event_id, is_system_user);
```

### Data Migration Strategy

1. **Migration File**: `20250725000001_add_is_system_user_to_event_permissions.exs`
   - Adds the new column with default value `false`
   - Creates necessary indexes

2. **Backfill Task**: `mix backfill_system_users`
   - Identifies existing security users by `sec_` prefix
   - Sets `is_system_user = true` for these records
   - Processes records in batches for performance
   - Includes dry-run mode for testing

### Code Changes

#### EventPermission Schema Updates

- Added `is_system_user` field to schema and type definitions
- Updated changesets to include the new field
- Added automatic flag setting for new security users with `sec_` prefix

#### Query Logic Updates

Updated `Events.get_security_user_by_event_id/2` to use the new flag:

```elixir
# Before
where: like(ep.user_document_id, ^"sec_%")

# After  
where: ep.is_system_user == true
```

#### Security User Creation Updates

All security user creation points now set `is_system_user: true`:

- Event creation (`Events.create_event/2`)
- Event copying (`EventCopier`)
- Entrance area creation (`EntranceAreas.create_entrance_area/1`)

## Migration Steps

### 1. Deploy Schema Changes

```bash
mix ecto.migrate
```

### 2. Backfill Existing Data

```bash
# Test first with dry run
mix backfill_system_users --dry-run

# Execute the backfill
mix backfill_system_users
```

### 3. Verify Migration

```bash
# Check that all sec_ users are flagged
mix ecto.sql -r EventsService.Repo -e "
SELECT COUNT(*) as total_sec_users 
FROM events.event_permissions 
WHERE user_document_id LIKE 'sec_%';

SELECT COUNT(*) as flagged_sec_users 
FROM events.event_permissions 
WHERE user_document_id LIKE 'sec_%' AND is_system_user = true;
"
```

### 4. Test System Functionality

Run the test suite to ensure all functionality works correctly:

```bash
mix test test/events_service/events/event_permission_system_user_test.exs
```

## Benefits

1. **Explicit Identification**: Clear boolean flag instead of string pattern matching
2. **UUID Migration Ready**: Removes dependency on string-based user identification
3. **Performance**: Indexed boolean queries are faster than LIKE operations
4. **Maintainability**: Reduces coupling between user identification and naming conventions
5. **Backward Compatibility**: Existing code continues to work during transition

## Future Considerations

1. **UUID Migration**: Once all security users are properly flagged, the system can migrate to UUID-only user identification
2. **Cleanup**: The `user_document_id` field can eventually be deprecated for system users
3. **Monitoring**: Add metrics to track system vs. human user activity

## Testing

The implementation includes comprehensive tests covering:

- Automatic flag setting for new security users
- Manual override capabilities
- Query functionality with the new flag
- Migration compatibility
- Edge cases and error conditions

## Rollback Plan

If issues arise, the migration can be rolled back:

1. Revert code changes to use the old `LIKE 'sec_%'` queries
2. Drop the new column and indexes if necessary
3. The backfill operation is idempotent and can be re-run safely
